import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [activeSection, setActiveSection] = useState('hero')
  const [isScrolled, setIsScrolled] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [scrollY, setScrollY] = useState(0)

  const galleryImages = [
    '/images/merbabu1.jpeg',
    '/images/merbabu2.jpg',
    '/images/merbabu3.jpg',
    '/images/merbabu4.jpg'
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
      setScrollY(window.scrollY)
    }
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener('scroll', handleScroll)
    window.addEventListener('mousemove', handleMouseMove)
    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('mousemove', handleMouseMove)
    }
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % galleryImages.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (activeSection === 'gallery') {
        if (e.key === 'ArrowLeft') {
          prevImage()
        } else if (e.key === 'ArrowRight') {
          nextImage()
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [activeSection])

  const scrollToSection = (sectionId) => {
    document.getElementById(sectionId).scrollIntoView({ behavior: 'smooth' })
    setActiveSection(sectionId)
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % galleryImages.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + galleryImages.length) % galleryImages.length)
  }

  return (
    <div className="App">
      {/* Navigation */}
      <nav className={`navbar ${isScrolled ? 'scrolled' : ''}`}>
        <div className="nav-container">
          <div className="nav-logo">
            <h2>🏔️ Merbabu</h2>
          </div>
          <ul className="nav-menu">
            <li><a href="#hero" onClick={() => scrollToSection('hero')}>Beranda</a></li>
            <li><a href="#about" onClick={() => scrollToSection('about')}>Tentang</a></li>
            <li><a href="#gallery" onClick={() => scrollToSection('gallery')}>Galeri</a></li>
            <li><a href="#info" onClick={() => scrollToSection('info')}>Info Pendakian</a></li>
            <li><a href="#contact" onClick={() => scrollToSection('contact')}>Kontak</a></li>
          </ul>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="hero">
        {/* Floating Particles */}
        <div className="particles">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 20}s`,
                animationDuration: `${15 + Math.random() * 10}s`
              }}
            />
          ))}
        </div>

        {/* Parallax Background Elements */}
        <div
          className="parallax-element parallax-1"
          style={{
            transform: `translateY(${scrollY * 0.5}px) translateX(${mousePosition.x * 0.02}px)`
          }}
        />
        <div
          className="parallax-element parallax-2"
          style={{
            transform: `translateY(${scrollY * 0.3}px) translateX(${mousePosition.x * -0.01}px)`
          }}
        />

        <div className="hero-content">
          <div className="hero-badge">
            <span className="badge-icon">🏔️</span>
            <span>Destinasi Terpopuler</span>
          </div>
          <h1 className="hero-title">
            <span className="title-main">Gunung</span>
            <span className="title-accent">Merbabu</span>
          </h1>
          <p className="hero-subtitle">Keindahan Alam yang Memukau di Jawa Tengah</p>
          <p className="hero-description">
            Nikmati pesona sunrise yang menakjubkan dan hamparan sabana yang luas
            di puncak Gunung Merbabu yang mempesona
          </p>
          <div className="hero-buttons">
            <button
              className="cta-button primary"
              onClick={() => scrollToSection('about')}
            >
              <span>Jelajahi Sekarang</span>
              <span className="button-icon">→</span>
            </button>
            <button
              className="cta-button secondary"
              onClick={() => scrollToSection('gallery')}
            >
              <span>Lihat Galeri</span>
              <span className="button-icon">📸</span>
            </button>
          </div>
        </div>

        <div className="hero-stats">
          <div className="stat glass-card">
            <div className="stat-icon">📏</div>
            <h3>3.145m</h3>
            <p>Ketinggian</p>
          </div>
          <div className="stat glass-card">
            <div className="stat-icon">⏱️</div>
            <h3>4-6 Jam</h3>
            <p>Waktu Pendakian</p>
          </div>
          <div className="stat glass-card">
            <div className="stat-icon">🛤️</div>
            <h3>5 Jalur</h3>
            <p>Rute Pendakian</p>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="scroll-indicator">
          <div className="scroll-mouse">
            <div className="scroll-wheel"></div>
          </div>
          <p>Scroll untuk menjelajah</p>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="about">
        <div className="container">
          <div className="section-header">
            <span className="section-badge">Tentang Kami</span>
            <h2 className="section-title">
              Pesona <span className="title-gradient">Gunung Merbabu</span>
            </h2>
            <p className="section-subtitle">
              Jelajahi keajaiban alam yang menakjubkan di salah satu gunung terindah di Jawa Tengah
            </p>
          </div>

          <div className="about-content">
            <div className="about-main">
              <div className="about-text glass-card">
                <div className="text-highlight">
                  <span className="highlight-number">01</span>
                  <div>
                    <h3>Sejarah & Lokasi</h3>
                    <p>
                      Gunung Merbabu adalah gunung berapi yang terletak di perbatasan
                      Kabupaten Magelang dan Kabupaten Boyolali, Jawa Tengah. Dengan
                      ketinggian 3.145 meter di atas permukaan laut, Merbabu menawarkan
                      pemandangan yang spektakuler.
                    </p>
                  </div>
                </div>

                <div className="text-highlight">
                  <span className="highlight-number">02</span>
                  <div>
                    <h3>Daya Tarik Utama</h3>
                    <p>
                      Gunung ini terkenal dengan sabana yang luas di puncaknya, sunrise
                      yang memukau, dan pemandangan Gunung Merapi yang gagah. Merbabu
                      menjadi destinasi favorit para pendaki karena jalurnya yang
                      relatif mudah namun tetap menantang.
                    </p>
                  </div>
                </div>
              </div>

              <div className="about-visual">
                <div className="visual-card">
                  <div className="card-image">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop" alt="Merbabu View" />
                    <div className="card-overlay">
                      <span className="overlay-text">Pemandangan Puncak</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="features-grid">
              <div className="feature modern-card">
                <div className="feature-header">
                  <div className="feature-icon gradient-icon">🌅</div>
                  <h4>Sunrise Spektakuler</h4>
                </div>
                <p>Nikmati matahari terbit yang memukau dari puncak dengan panorama 360 derajat</p>
                <div className="feature-stats">
                  <span className="stat-item">
                    <strong>05:30</strong>
                    <small>Waktu Sunrise</small>
                  </span>
                </div>
              </div>

              <div className="feature modern-card">
                <div className="feature-header">
                  <div className="feature-icon gradient-icon">🌿</div>
                  <h4>Sabana Luas</h4>
                </div>
                <p>Hamparan padang rumput yang indah membentang luas di puncak gunung</p>
                <div className="feature-stats">
                  <span className="stat-item">
                    <strong>2km²</strong>
                    <small>Luas Sabana</small>
                  </span>
                </div>
              </div>

              <div className="feature modern-card">
                <div className="feature-header">
                  <div className="feature-icon gradient-icon">🏔️</div>
                  <h4>View Merapi</h4>
                </div>
                <p>Pemandangan Gunung Merapi yang menawan dari sudut pandang yang unik</p>
                <div className="feature-stats">
                  <span className="stat-item">
                    <strong>15km</strong>
                    <small>Jarak ke Merapi</small>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section id="gallery" className="gallery">
        <div className="container">
          <div className="section-header">
            <span className="section-badge">Galeri</span>
            <h2 className="section-title">
              Koleksi <span className="title-gradient">Foto Terbaik</span>
            </h2>
            <p className="section-subtitle">
              Saksikan keindahan Gunung Merbabu melalui lensa para fotografer profesional
            </p>
          </div>

          <div className="gallery-container">
            <div className="main-gallery">
              <div className="slider-container modern-slider">
                <button className="slider-arrow slider-arrow-left glass-button" onClick={prevImage}>
                  <span>‹</span>
                </button>

                <div className="image-container">
                  <img
                    src={galleryImages[currentImageIndex]}
                    alt={`Gunung Merbabu ${currentImageIndex + 1}`}
                    className="slider-image"
                    onClick={nextImage}
                    title="Klik untuk foto selanjutnya"
                  />
                  <div className="image-overlay">
                    <div className="overlay-content">
                      <h3>Keindahan Merbabu</h3>
                      <p>Foto {currentImageIndex + 1} dari {galleryImages.length}</p>
                    </div>
                  </div>
                </div>

                <button className="slider-arrow slider-arrow-right glass-button" onClick={nextImage}>
                  <span>›</span>
                </button>

                <div className="slider-counter glass-counter">
                  <span className="counter-current">{currentImageIndex + 1}</span>
                  <span className="counter-divider">/</span>
                  <span className="counter-total">{galleryImages.length}</span>
                </div>
              </div>

              <div className="gallery-controls">
                <div className="slider-dots modern-dots">
                  {galleryImages.map((_, index) => (
                    <button
                      key={index}
                      className={`dot ${index === currentImageIndex ? 'active' : ''}`}
                      onClick={() => setCurrentImageIndex(index)}
                    >
                      <span className="dot-inner"></span>
                    </button>
                  ))}
                </div>

                <div className="gallery-actions">
                  <button className="action-btn" title="Fullscreen">
                    <span>⛶</span>
                  </button>
                  <button className="action-btn" title="Download">
                    <span>⬇</span>
                  </button>
                  <button className="action-btn" title="Share">
                    <span>⤴</span>
                  </button>
                </div>
              </div>
            </div>

            <div className="gallery-thumbnails">
              {galleryImages.map((image, index) => (
                <div
                  key={index}
                  className={`thumbnail ${index === currentImageIndex ? 'active' : ''}`}
                  onClick={() => setCurrentImageIndex(index)}
                >
                  <img src={image} alt={`Thumbnail ${index + 1}`} />
                  <div className="thumbnail-overlay">
                    <span>{index + 1}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Info Section */}
      <section id="info" className="info">
        <div className="container">
          <h2 className="section-title">Informasi Pendakian</h2>
          <div className="info-grid">
            <div className="info-card">
              <h3>🚶‍♂️ Jalur Pendakian</h3>
              <ul>
                <li>Selo (paling populer)</li>
                <li>Wekas</li>
                <li>Thekelan</li>
                <li>Cunthel</li>
                <li>Suwanting</li>
              </ul>
            </div>
            <div className="info-card">
              <h3>🎒 Persiapan</h3>
              <ul>
                <li>Jaket tebal & sleeping bag</li>
                <li>Headlamp & senter</li>
                <li>Makanan & air yang cukup</li>
                <li>Obat-obatan pribadi</li>
                <li>Tenda (jika bermalam)</li>
              </ul>
            </div>
            <div className="info-card">
              <h3>⏰ Waktu Terbaik</h3>
              <ul>
                <li>Musim kemarau (April-Oktober)</li>
                <li>Hindari musim hujan</li>
                <li>Start pendakian pagi/sore</li>
                <li>Cuaca cerah untuk sunrise</li>
              </ul>
            </div>
            <div className="info-card">
              <h3>💰 Biaya</h3>
              <ul>
                <li>Tiket masuk: Rp 25.000</li>
                <li>Parkir: Rp 5.000</li>
                <li>Porter (opsional): Rp 300.000</li>
                <li>Guide (opsional): Rp 200.000</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="contact">
        <div className="container">
          <h2 className="section-title">Kontak & Informasi</h2>
          <div className="contact-content">
            <div className="contact-info">
              <div className="contact-item">
                <span className="contact-icon">📍</span>
                <div>
                  <h4>Lokasi</h4>
                  <p>Perbatasan Magelang & Boyolali, Jawa Tengah</p>
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📞</span>
                <div>
                  <h4>Kontak Basecamp</h4>
                  <p>+62 812-3456-7890</p>
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">🕐</span>
                <div>
                  <h4>Jam Operasional</h4>
                  <p>24 Jam (Pendaftaran 06:00-18:00)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <p>&copy; 2024 Gunung Merbabu Landing Page. Dibuat dengan ❤️ untuk para pecinta alam.</p>
        </div>
      </footer>
    </div>
  )
}

export default App
