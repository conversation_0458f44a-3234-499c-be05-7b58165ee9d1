@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-coffee-800 hover:bg-coffee-900 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-coffee-800 text-coffee-800 hover:bg-coffee-800 hover:text-white font-semibold py-3 px-8 rounded-full transition-all duration-300;
  }

  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-coffee-800 to-coffee-600 bg-clip-text text-transparent;
  }

  .card-hover {
    @apply transition-all duration-300 transform hover:scale-105 hover:shadow-2xl;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md border border-white/20 shadow-xl;
  }
}
