* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

.App {
  overflow-x: hidden;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: all 0.3s ease;
  padding: 1rem 0;
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.nav-logo h2 {
  color: #2c5530;
  font-size: 1.5rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
  cursor: pointer;
}

.nav-menu a:hover {
  color: #2c5530;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  height: auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image:
    linear-gradient(rgba(44, 85, 48, 0.6), rgba(44, 85, 48, 0.8)),
    url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  padding: 6rem 0 4rem 0;
}

/* Floating Particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Parallax Elements */
.parallax-element {
  position: absolute;
  pointer-events: none;
  z-index: 1;
}

.parallax-1 {
  top: 20%;
  left: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.parallax-2 {
  bottom: 20%;
  right: 15%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

/* Hero Badge */
.hero-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: fadeInUp 1s ease 0.2s both;
}

.badge-icon {
  font-size: 1.2rem;
}

.hero-content {
  max-width: 900px;
  padding: 0 2rem;
  animation: fadeInUp 1s ease;
  z-index: 2;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 2rem;
}

.hero-title {
  font-size: 5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.1;
}

.title-main {
  display: block;
  color: white;
}

.title-accent {
  display: block;
  background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.8;
  line-height: 1.8;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 2rem 0;
}

.cta-button {
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-button:hover {
  transform: translateY(-3px);
}

.cta-button.primary:hover {
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.5);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.button-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.cta-button:hover .button-icon {
  transform: translateX(3px);
}

.hero-stats {
  display: flex;
  gap: 2rem;
  animation: fadeInUp 1s ease 0.5s both;
  z-index: 2;
  justify-content: center;
  margin-top: 2rem;
  margin-bottom: 0;
}

.stat.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 120px;
  margin-bottom: 1rem;
}

.stat.glass-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.stat h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
}

.stat p {
  opacity: 0.9;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Scroll Indicator */
.scroll-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  animation: fadeInUp 1s ease 1s both;
  z-index: 2;
  margin-top: 2rem;
}

.scroll-mouse {
  width: 24px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  position: relative;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: scrollWheel 2s infinite;
}

@keyframes scrollWheel {
  0% { top: 6px; opacity: 1; }
  50% { top: 16px; opacity: 0.5; }
  100% { top: 6px; opacity: 1; }
}

.scroll-indicator p {
  font-size: 0.8rem;
  opacity: 0.7;
  margin: 0;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Glass Card Effect */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.glass-button {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-badge {
  display: inline-block;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.section-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #2c5530;
  line-height: 1.2;
}

.title-gradient {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24, #feca57);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* About Section */
.about {
  padding: 8rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, rgba(248, 249, 250, 0), rgba(248, 249, 250, 1));
  z-index: 1;
}

.about-main {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 4rem;
  margin-bottom: 4rem;
  align-items: start;
}

.about-text.glass-card {
  padding: 3rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.text-highlight {
  display: flex;
  gap: 2rem;
  margin-bottom: 2.5rem;
  align-items: flex-start;
}

.highlight-number {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.text-highlight h3 {
  font-size: 1.4rem;
  color: #2c5530;
  margin-bottom: 1rem;
  font-weight: 700;
}

.text-highlight p {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.8;
  margin: 0;
}

.about-visual {
  position: relative;
}

.visual-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.visual-card:hover {
  transform: translateY(-10px) rotate(2deg);
}

.card-image {
  position: relative;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.visual-card:hover .card-image img {
  transform: scale(1.1);
}

.card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem;
  color: white;
}

.overlay-text {
  font-size: 1.2rem;
  font-weight: 600;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.feature.modern-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.feature.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24, #feca57);
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

.feature.modern-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.feature-icon.gradient-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: white;
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.feature h4 {
  font-size: 1.4rem;
  color: #2c5530;
  font-weight: 700;
  margin: 0;
}

.feature p {
  color: #666;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.feature-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  min-width: 80px;
}

.stat-item strong {
  font-size: 1.2rem;
  color: #2c5530;
  font-weight: 700;
}

.stat-item small {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

/* Gallery Section */
.gallery {
  padding: 8rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.gallery::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  z-index: 0;
}

.gallery .container {
  position: relative;
  z-index: 1;
}

.gallery-container {
  max-width: 1000px;
  margin: 0 auto;
}

.main-gallery {
  margin-bottom: 3rem;
}

.slider-container.modern-slider {
  position: relative;
  border-radius: 25px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.image-container {
  position: relative;
  overflow: hidden;
}

.slider-arrow.glass-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: bold;
  color: #2c5530;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.slider-arrow.glass-button:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: translateY(-50%) scale(1.15);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.slider-arrow-left {
  left: 25px;
}

.slider-arrow-right {
  right: 25px;
}

.slider-arrow span {
  line-height: 1;
  margin-top: -2px;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem;
  color: white;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.slider-container:hover .image-overlay {
  transform: translateY(0);
}

.overlay-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.overlay-content p {
  opacity: 0.9;
  margin: 0;
}

.slider-counter.glass-counter {
  position: absolute;
  top: 25px;
  right: 25px;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 5px;
}

.counter-current {
  color: #feca57;
  font-size: 18px;
}

.counter-divider {
  opacity: 0.6;
}

.counter-total {
  opacity: 0.8;
}

.gallery-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 0 1rem;
}

.slider-dots.modern-dots {
  display: flex;
  gap: 1rem;
}

.dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #ddd;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding: 0;
}

.dot:hover {
  border-color: #2c5530;
  transform: scale(1.2);
}

.dot.active {
  border-color: #2c5530;
  background: #2c5530;
  transform: scale(1.3);
}

.dot-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dot.active .dot-inner {
  opacity: 1;
}

.gallery-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(44, 85, 48, 0.1);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #2c5530;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #2c5530;
  color: white;
  transform: scale(1.1);
}

.gallery-thumbnails {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.thumbnail:hover {
  transform: scale(1.1);
  border-color: #2c5530;
}

.thumbnail.active {
  border-color: #ff6b6b;
  transform: scale(1.15);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.thumbnail:hover .thumbnail-overlay {
  opacity: 1;
}

.slider-image {
  width: 100%;
  height: 500px;
  object-fit: cover;
  transition: all 0.5s ease;
  cursor: pointer;
}

.slider-image:hover {
  transform: scale(1.02);
}

.slider-dots {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: #2c5530;
  transform: scale(1.2);
}

/* Info Section */
.info {
  padding: 6rem 0;
  background: #f8f9fa;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.info-card {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
}

.info-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  color: #2c5530;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-card ul {
  list-style: none;
}

.info-card li {
  padding: 0.5rem 0;
  color: #555;
  border-bottom: 1px solid #eee;
  transition: color 0.3s ease;
}

.info-card li:hover {
  color: #2c5530;
}

.info-card li:last-child {
  border-bottom: none;
}

/* Contact Section */
.contact {
  padding: 6rem 0;
  background: white;
}

.contact-content {
  max-width: 600px;
  margin: 0 auto;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.contact-item:hover {
  transform: translateX(10px);
}

.contact-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #2c5530, #4a7c59);
  color: white;
  border-radius: 50%;
}

.contact-item h4 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #2c5530;
}

.contact-item p {
  color: #666;
}

/* Footer */
.footer {
  background: #2c5530;
  color: white;
  text-align: center;
  padding: 2rem 0;
}

.footer p {
  opacity: 0.8;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .hero {
    padding: 4rem 0 2rem 0;
    min-height: 100vh;
  }

  .hero-title {
    font-size: 3rem;
  }

  .title-main, .title-accent {
    display: inline;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 280px;
  }

  .hero-content {
    margin-top: 1rem;
  }

  .hero-stats {
    flex-direction: row;
    gap: 1rem;
    margin-top: 2rem;
    margin-bottom: 0;
    flex-wrap: wrap;
    justify-content: center;
  }

  .stat.glass-card {
    min-width: 100px;
    padding: 1rem;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .about-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-text.glass-card {
    padding: 2rem;
  }

  .text-highlight {
    flex-direction: column;
    gap: 1rem;
  }

  .highlight-number {
    align-self: flex-start;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature.modern-card {
    padding: 2rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .slider-image {
    height: 300px;
  }

  .slider-arrow.glass-button {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .slider-arrow-left {
    left: 15px;
  }

  .slider-arrow-right {
    right: 15px;
  }

  .slider-counter.glass-counter {
    top: 15px;
    right: 15px;
    padding: 8px 16px;
    font-size: 14px;
  }

  .gallery-controls {
    flex-direction: column;
    gap: 1.5rem;
  }

  .gallery-thumbnails {
    gap: 0.5rem;
  }

  .thumbnail {
    width: 60px;
    height: 45px;
  }

  .particles {
    display: none;
  }

  .parallax-element {
    display: none;
  }

  .scroll-indicator {
    display: none;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 3rem 0 1rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .nav-container {
    padding: 0 1rem;
  }

  .hero-content {
    padding: 0 1rem;
    margin-top: 0.5rem;
  }

  .hero-badge {
    padding: 0.4rem 1rem;
    font-size: 0.8rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .hero-stats {
    gap: 0.5rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
  }

  .stat.glass-card {
    padding: 0.8rem;
    min-width: 80px;
    flex: 1;
    max-width: 100px;
  }

  .stat h3 {
    font-size: 1.4rem;
  }

  .stat p {
    font-size: 0.75rem;
  }

  .cta-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .scroll-indicator {
    margin-top: 1rem;
  }
}
